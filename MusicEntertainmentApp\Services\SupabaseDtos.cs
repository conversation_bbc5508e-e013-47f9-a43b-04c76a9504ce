using MusicEntertainmentApp.Models;
using Postgrest.Attributes;
using Postgrest.Models;

namespace MusicEntertainmentApp.Services;

[Table("user_profiles")]
public class UserProfileDto : BaseModel
{
    [PrimaryKey("id")]
    public Guid Id { get; set; }

    [Column("email")]
    public string? Email { get; set; }

    [Column("full_name")]
    public string? FullName { get; set; }

    [Column("role")]
    public string? Role { get; set; }

    [Column("avatar_url")]
    public string? AvatarUrl { get; set; }

    [Column("created_at")]
    public DateTime CreatedAt { get; set; }

    [Column("updated_at")]
    public DateTime UpdatedAt { get; set; }

    public UserProfile ToModel()
    {
        return new UserProfile
        {
            Id = Id,
            Email = Email ?? string.Empty,
            FullName = FullName,
            Role = Enum.TryParse<UserRole>(Role, true, out var role) ? role : UserRole.Viewer,
            AvatarUrl = AvatarUrl,
            CreatedAt = CreatedAt,
            UpdatedAt = UpdatedAt
        };
    }

    public static UserProfileDto FromModel(UserProfile model)
    {
        return new UserProfileDto
        {
            Id = model.Id,
            Email = model.Email,
            FullName = model.FullName,
            Role = model.Role.ToString().ToLower(),
            AvatarUrl = model.AvatarUrl,
            CreatedAt = model.CreatedAt,
            UpdatedAt = model.UpdatedAt
        };
    }
}

[Table("artists")]
public class ArtistDto : BaseModel
{
    [PrimaryKey("id")]
    public Guid Id { get; set; }

    [Column("name")]
    public string? Name { get; set; }

    [Column("bio")]
    public string? Bio { get; set; }

    [Column("image_url")]
    public string? ImageUrl { get; set; }

    [Column("spotify_id")]
    public string? SpotifyId { get; set; }

    [Column("apple_music_id")]
    public string? AppleMusicId { get; set; }

    [Column("youtube_channel_id")]
    public string? YouTubeChannelId { get; set; }

    [Column("social_links")]
    public string? SocialLinksJson { get; set; }

    [Column("metadata")]
    public string? MetadataJson { get; set; }

    [Column("created_at")]
    public DateTime CreatedAt { get; set; }

    [Column("updated_at")]
    public DateTime UpdatedAt { get; set; }

    [Column("created_by")]
    public Guid? CreatedBy { get; set; }

    public Artist ToModel()
    {
        return new Artist
        {
            Id = Id,
            Name = Name ?? string.Empty,
            Bio = Bio,
            ImageUrl = ImageUrl,
            SpotifyId = SpotifyId,
            AppleMusicId = AppleMusicId,
            YouTubeChannelId = YouTubeChannelId,
            SocialLinksJson = SocialLinksJson ?? "{}",
            MetadataJson = MetadataJson ?? "{}",
            CreatedAt = CreatedAt,
            UpdatedAt = UpdatedAt,
            CreatedBy = CreatedBy
        };
    }

    public static ArtistDto FromModel(Artist model)
    {
        return new ArtistDto
        {
            Id = model.Id,
            Name = model.Name,
            Bio = model.Bio,
            ImageUrl = model.ImageUrl,
            SpotifyId = model.SpotifyId,
            AppleMusicId = model.AppleMusicId,
            YouTubeChannelId = model.YouTubeChannelId,
            SocialLinksJson = model.SocialLinksJson,
            MetadataJson = model.MetadataJson,
            CreatedAt = model.CreatedAt,
            UpdatedAt = model.UpdatedAt,
            CreatedBy = model.CreatedBy
        };
    }
}

[Table("labels")]
public class LabelDto : BaseModel
{
    [PrimaryKey("id")]
    public Guid Id { get; set; }

    [Column("name")]
    public string? Name { get; set; }

    [Column("contact_email")]
    public string? ContactEmail { get; set; }

    [Column("contact_phone")]
    public string? ContactPhone { get; set; }

    [Column("website")]
    public string? Website { get; set; }

    [Column("created_at")]
    public DateTime CreatedAt { get; set; }

    [Column("updated_at")]
    public DateTime UpdatedAt { get; set; }

    public Label ToModel()
    {
        return new Label
        {
            Id = Id,
            Name = Name ?? string.Empty,
            ContactEmail = ContactEmail,
            ContactPhone = ContactPhone,
            Website = Website,
            CreatedAt = CreatedAt,
            UpdatedAt = UpdatedAt
        };
    }

    public static LabelDto FromModel(Label model)
    {
        return new LabelDto
        {
            Id = model.Id,
            Name = model.Name,
            ContactEmail = model.ContactEmail,
            ContactPhone = model.ContactPhone,
            Website = model.Website,
            CreatedAt = model.CreatedAt,
            UpdatedAt = model.UpdatedAt
        };
    }
}

[Table("releases")]
public class ReleaseDto : BaseModel
{
    [PrimaryKey("id")]
    public Guid Id { get; set; }

    [Column("title")]
    public string? Title { get; set; }

    [Column("artist_id")]
    public Guid? ArtistId { get; set; }

    [Column("label_id")]
    public Guid? LabelId { get; set; }

    [Column("release_date")]
    public DateTime? ReleaseDate { get; set; }

    [Column("status")]
    public string? Status { get; set; }

    [Column("genre")]
    public string? Genre { get; set; }

    [Column("cover_art_url")]
    public string? CoverArtUrl { get; set; }

    [Column("upc_code")]
    public string? UpcCode { get; set; }

    [Column("catalog_number")]
    public string? CatalogNumber { get; set; }

    [Column("notes")]
    public string? Notes { get; set; }

    [Column("metadata")]
    public string? MetadataJson { get; set; }

    [Column("created_at")]
    public DateTime CreatedAt { get; set; }

    [Column("updated_at")]
    public DateTime UpdatedAt { get; set; }

    [Column("created_by")]
    public Guid? CreatedBy { get; set; }

    public Release ToModel()
    {
        return new Release
        {
            Id = Id,
            Title = Title ?? string.Empty,
            ArtistId = ArtistId,
            LabelId = LabelId,
            ReleaseDate = ReleaseDate,
            Status = Enum.TryParse<ReleaseStatus>(Status, true, out var status) ? status : ReleaseStatus.Draft,
            Genre = Genre,
            CoverArtUrl = CoverArtUrl,
            UpcCode = UpcCode,
            CatalogNumber = CatalogNumber,
            Notes = Notes,
            MetadataJson = MetadataJson ?? "{}",
            CreatedAt = CreatedAt,
            UpdatedAt = UpdatedAt,
            CreatedBy = CreatedBy
        };
    }

    public static ReleaseDto FromModel(Release model)
    {
        return new ReleaseDto
        {
            Id = model.Id,
            Title = model.Title,
            ArtistId = model.ArtistId,
            LabelId = model.LabelId,
            ReleaseDate = model.ReleaseDate,
            Status = model.Status.ToString().ToLower(),
            Genre = model.Genre,
            CoverArtUrl = model.CoverArtUrl,
            UpcCode = model.UpcCode,
            CatalogNumber = model.CatalogNumber,
            Notes = model.Notes,
            MetadataJson = model.MetadataJson,
            CreatedAt = model.CreatedAt,
            UpdatedAt = model.UpdatedAt,
            CreatedBy = model.CreatedBy
        };
    }
}
