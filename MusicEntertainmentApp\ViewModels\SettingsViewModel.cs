using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MusicEntertainmentApp.Models;
using MusicEntertainmentApp.Services;

namespace MusicEntertainmentApp.ViewModels;

public partial class SettingsViewModel : BaseViewModel
{
    [ObservableProperty]
    private bool _isDarkTheme = true;

    [ObservableProperty]
    private int _defaultPageSize = 50;

    [ObservableProperty]
    private string _defaultExportPath = string.Empty;

    [ObservableProperty]
    private bool _showTooltips = true;

    [ObservableProperty]
    private bool _autoSave = true;

    [ObservableProperty]
    private double _minPlaylistDuration = 2.5;

    [ObservableProperty]
    private double _maxPlaylistDuration = 3.0;

    [ObservableProperty]
    private int _maxTracksPerAlbum = 2;

    public SettingsViewModel(ISupabaseService supabaseService, ILogger<SettingsViewModel> logger)
        : base(supabaseService, logger)
    {
        LoadSettings();
    }

    [RelayCommand]
    private async Task LoadSettingsAsync()
    {
        await ExecuteAsync(async () =>
        {
            var currentUser = await _supabaseService.GetCurrentUserProfileAsync();
            if (currentUser == null) return;

            // Load user preferences
            var themeSettings = await _supabaseService.GetSettingAsync("theme", currentUser.Id);
            if (themeSettings != null)
            {
                IsDarkTheme = themeSettings.SettingValue?.ToString() == "Dark";
            }

            var pageSizeSettings = await _supabaseService.GetSettingAsync("pageSize", currentUser.Id);
            if (pageSizeSettings != null && int.TryParse(themeSettings.SettingValue?.ToString(), out var pageSize))
            {
                DefaultPageSize = pageSize;
            }

            // Load other settings...
            
            _logger.LogInformation("Settings loaded for user {UserId}", currentUser.Id);
        });
    }

    [RelayCommand]
    private async Task SaveSettingsAsync()
    {
        await ExecuteAsync(async () =>
        {
            var currentUser = await _supabaseService.GetCurrentUserProfileAsync();
            if (currentUser == null) return;

            // Save user preferences
            await _supabaseService.SaveSettingAsync("theme", IsDarkTheme ? "Dark" : "Light", currentUser.Id);
            await _supabaseService.SaveSettingAsync("pageSize", DefaultPageSize, currentUser.Id);
            await _supabaseService.SaveSettingAsync("defaultExportPath", DefaultExportPath, currentUser.Id);
            await _supabaseService.SaveSettingAsync("showTooltips", ShowTooltips, currentUser.Id);
            await _supabaseService.SaveSettingAsync("autoSave", AutoSave, currentUser.Id);

            // Save playlist generator settings
            var playlistSettings = new PlaylistGeneratorSettings
            {
                MinDurationHours = MinPlaylistDuration,
                MaxDurationHours = MaxPlaylistDuration,
                MaxTracksPerAlbum = MaxTracksPerAlbum
            };
            await _supabaseService.SaveSettingAsync("playlistGenerator", playlistSettings, currentUser.Id);

            ShowSuccess("Settings saved successfully.");
            _logger.LogInformation("Settings saved for user {UserId}", currentUser.Id);
        });
    }

    [RelayCommand]
    private void ResetToDefaults()
    {
        IsDarkTheme = true;
        DefaultPageSize = 50;
        DefaultExportPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
        ShowTooltips = true;
        AutoSave = true;
        MinPlaylistDuration = 2.5;
        MaxPlaylistDuration = 3.0;
        MaxTracksPerAlbum = 2;

        ShowSuccess("Settings reset to defaults.");
    }

    [RelayCommand]
    private void BrowseExportPath()
    {
        var dialog = new Microsoft.Win32.OpenFolderDialog
        {
            Title = "Select Default Export Folder"
        };

        if (dialog.ShowDialog() == true)
        {
            DefaultExportPath = dialog.FolderName;
        }
    }

    private void LoadSettings()
    {
        _ = LoadSettingsAsync();
    }
}
