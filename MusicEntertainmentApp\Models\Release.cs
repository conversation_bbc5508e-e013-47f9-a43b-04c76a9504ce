using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace MusicEntertainmentApp.Models;

public class Release : BaseEntity
{
    [Required]
    [StringLength(300)]
    public string Title { get; set; } = string.Empty;
    
    public Guid? ArtistId { get; set; }
    
    public Guid? LabelId { get; set; }
    
    public DateTime? ReleaseDate { get; set; }
    
    public ReleaseStatus Status { get; set; } = ReleaseStatus.Draft;
    
    public string? Genre { get; set; }
    
    public string? CoverArtUrl { get; set; }
    
    public string? UpcCode { get; set; }
    
    public string? CatalogNumber { get; set; }
    
    public string? Notes { get; set; }
    
    // JSON field for additional metadata
    public string MetadataJson { get; set; } = "{}";
    
    [JsonIgnore]
    public Dictionary<string, object> Metadata
    {
        get => JsonConvert.DeserializeObject<Dictionary<string, object>>(MetadataJson) ?? new Dictionary<string, object>();
        set => MetadataJson = JsonConvert.SerializeObject(value);
    }
    
    // Navigation properties
    public virtual Artist? Artist { get; set; }
    public virtual Label? Label { get; set; }
    public virtual UserProfile? Creator { get; set; }
    public virtual ICollection<Track> Tracks { get; set; } = new List<Track>();
    public virtual ICollection<FileEntity> Files { get; set; } = new List<FileEntity>();
    
    // Computed properties
    public int TrackCount => Tracks?.Count ?? 0;
    public string ArtistName => Artist?.Name ?? "Unknown Artist";
    public string LabelName => Label?.Name ?? "Unknown Label";
}
