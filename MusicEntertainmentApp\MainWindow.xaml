﻿<Window x:Class="MusicEntertainmentApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:MusicEntertainmentApp"
        mc:Ignorable="d"
        Title="Music Entertainment App"
        Height="900"
        Width="1400"
        WindowState="Maximized"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>

        <!-- Dark Theme Resources -->
        <ResourceDictionary x:Key="DarkTheme">
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Dark" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>

        <!-- Light Theme Resources -->
        <ResourceDictionary x:Key="LightTheme">
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Sidebar -->
        <Border Grid.Column="0"
                Background="{DynamicResource PrimaryHueDarkBrush}"
                Width="{Binding IsSidebarExpanded, Converter={StaticResource BoolToWidthConverter}, ConverterParameter='250|60'}">

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header -->
                <StackPanel Grid.Row="0" Margin="15,20,15,20">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🎵" FontSize="24" Foreground="White" VerticalAlignment="Center"/>
                        <TextBlock Text="Music App"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="White"
                                   Margin="10,0,0,0"
                                   VerticalAlignment="Center"
                                   Visibility="{Binding IsSidebarExpanded, Converter={StaticResource BoolToVisConverter}}"/>
                    </StackPanel>

                    <Button Command="{Binding ToggleSidebarCommand}"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Foreground="White"
                            HorizontalAlignment="Right"
                            Margin="0,10,0,0">
                        <materialDesign:PackIcon Kind="Menu"/>
                    </Button>
                </StackPanel>

                <!-- Navigation Items -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <ItemsControl ItemsSource="{Binding NavigationItems}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Button Command="{Binding DataContext.NavigateToTabCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                        CommandParameter="{Binding Route}"
                                        Style="{StaticResource MaterialDesignFlatButton}"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Foreground="White"
                                        Padding="15,10"
                                        Margin="5,2">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="{Binding Icon}" FontSize="18" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding Name}"
                                                   Margin="15,0,0,0"
                                                   VerticalAlignment="Center"
                                                   Visibility="{Binding DataContext.IsSidebarExpanded, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource BoolToVisConverter}}"/>
                                    </StackPanel>
                                </Button>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>

                <!-- User Info -->
                <StackPanel Grid.Row="2" Margin="15,20">
                    <Button Command="{Binding ShowUserProfileCommand}"
                            Style="{StaticResource MaterialDesignFlatButton}"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Foreground="White"
                            Padding="10">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="32" Height="32" Fill="White">
                                <Ellipse.OpacityMask>
                                    <ImageBrush ImageSource="{Binding CurrentUser.AvatarUrl}" Stretch="UniformToFill"/>
                                </Ellipse.OpacityMask>
                            </Ellipse>
                            <StackPanel Margin="10,0,0,0"
                                        Visibility="{Binding IsSidebarExpanded, Converter={StaticResource BoolToVisConverter}}">
                                <TextBlock Text="{Binding CurrentUser.FullName}"
                                           FontWeight="Bold"
                                           FontSize="12"/>
                                <TextBlock Text="{Binding CurrentUser.Role}"
                                           FontSize="10"
                                           Opacity="0.8"/>
                            </StackPanel>
                        </StackPanel>
                    </Button>

                    <StackPanel Orientation="Horizontal"
                                HorizontalAlignment="Center"
                                Margin="0,10,0,0">
                        <Button Command="{Binding ToggleThemeCommand}"
                                Style="{StaticResource MaterialDesignIconButton}"
                                Foreground="White"
                                ToolTip="Toggle Theme">
                            <materialDesign:PackIcon Kind="Brightness6"/>
                        </Button>

                        <Button Command="{Binding SignOutCommand}"
                                Style="{StaticResource MaterialDesignIconButton}"
                                Foreground="White"
                                ToolTip="Sign Out">
                            <materialDesign:PackIcon Kind="Logout"/>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Top Bar -->
            <Border Grid.Row="0"
                    Background="{DynamicResource MaterialDesignCardBackground}"
                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                    BorderThickness="0,0,0,1"
                    Padding="20,15">

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Search Bar -->
                    <TextBox Grid.Column="0"
                             Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="Search releases, artists, tracks..."
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             MaxWidth="400"
                             HorizontalAlignment="Left">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding GlobalSearchCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Command="{Binding GlobalSearchCommand}"
                                Style="{StaticResource MaterialDesignIconButton}"
                                ToolTip="Search">
                            <materialDesign:PackIcon Kind="Magnify"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Content Area -->
            <ContentControl Grid.Row="1"
                            Content="{Binding SelectedTabContent}"
                            Margin="20"/>

            <!-- Status Bar -->
            <StatusBar Grid.Row="2"
                       Background="{DynamicResource MaterialDesignCardBackground}"
                       BorderBrush="{DynamicResource MaterialDesignDivider}"
                       BorderThickness="0,1,0,0">

                <StatusBarItem>
                    <TextBlock Text="{Binding SelectedTab}"/>
                </StatusBarItem>

                <Separator/>

                <StatusBarItem>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Loading"
                                                 Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}}"
                                                 Margin="0,0,5,0"/>
                        <TextBlock Text="{Binding StatusMessage}"/>
                    </StackPanel>
                </StatusBarItem>

                <StatusBarItem HorizontalAlignment="Right">
                    <TextBlock Text="{Binding CurrentUser.Email}"/>
                </StatusBarItem>
            </StatusBar>
        </Grid>
    </Grid>
</Window>
