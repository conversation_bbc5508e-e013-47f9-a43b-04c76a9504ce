using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MusicEntertainmentApp.Models;
using MusicEntertainmentApp.Services;
using System.Collections.ObjectModel;

namespace MusicEntertainmentApp.ViewModels;

public partial class TracksViewModel : BaseViewModel
{
    [ObservableProperty]
    private ObservableCollection<Track> _tracks = new();

    [ObservableProperty]
    private string _searchText = string.Empty;

    [ObservableProperty]
    private TrackStatus? _selectedStatus;

    [ObservableProperty]
    private Track? _selectedTrack;

    public ObservableCollection<TrackStatus> AvailableStatuses { get; } = new();

    public TracksViewModel(ISupabaseService supabaseService, ILogger<TracksViewModel> logger)
        : base(supabaseService, logger)
    {
        InitializeStatuses();
        LoadTracks();
    }

    private void InitializeStatuses()
    {
        foreach (var status in Enum.GetValues<TrackStatus>())
        {
            AvailableStatuses.Add(status);
        }
    }

    [RelayCommand]
    private async Task LoadTracksAsync()
    {
        await ExecuteAsync(async () =>
        {
            var tracks = await _supabaseService.GetTracksAsync(search: SearchText, status: SelectedStatus);
            
            Tracks.Clear();
            foreach (var track in tracks)
            {
                Tracks.Add(track);
            }
            
            _logger.LogInformation("Loaded {Count} tracks", Tracks.Count);
        });
    }

    [RelayCommand]
    private void EditTrack(Track? track)
    {
        if (track == null) return;
        
        SelectedTrack = track;
        ShowSuccess($"Edit dialog for '{track.Title}' will be implemented soon.");
    }

    [RelayCommand]
    private async Task DeleteTrackAsync(Track? track)
    {
        if (track == null) return;

        var result = System.Windows.MessageBox.Show(
            $"Are you sure you want to delete the track '{track.Title}'?",
            "Confirm Delete",
            System.Windows.MessageBoxButton.YesNo,
            System.Windows.MessageBoxImage.Warning);

        if (result == System.Windows.MessageBoxResult.Yes)
        {
            await ExecuteAsync(async () =>
            {
                await _supabaseService.DeleteTrackAsync(track.Id);
                Tracks.Remove(track);
                ShowSuccess($"Track '{track.Title}' deleted successfully.");
            });
        }
    }

    private void LoadTracks()
    {
        _ = LoadTracksAsync();
    }

    partial void OnSearchTextChanged(string value)
    {
        _ = LoadTracksAsync();
    }

    partial void OnSelectedStatusChanged(TrackStatus? value)
    {
        _ = LoadTracksAsync();
    }
}
