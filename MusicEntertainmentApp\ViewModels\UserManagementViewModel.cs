using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MusicEntertainmentApp.Models;
using MusicEntertainmentApp.Services;
using System.Collections.ObjectModel;

namespace MusicEntertainmentApp.ViewModels;

public partial class UserManagementViewModel : BaseViewModel
{
    [ObservableProperty]
    private ObservableCollection<UserProfile> _users = new();

    [ObservableProperty]
    private UserProfile? _selectedUser;

    [ObservableProperty]
    private bool _isAddingNew;

    [ObservableProperty]
    private string _newUserEmail = string.Empty;

    [ObservableProperty]
    private string _newUserFullName = string.Empty;

    [ObservableProperty]
    private UserRole _newUserRole = UserRole.Viewer;

    public ObservableCollection<UserRole> AvailableRoles { get; } = new();

    public UserManagementViewModel(ISupabaseService supabaseService, ILogger<UserManagementViewModel> logger)
        : base(supabaseService, logger)
    {
        InitializeRoles();
        LoadUsers();
    }

    private void InitializeRoles()
    {
        foreach (var role in Enum.GetValues<UserRole>())
        {
            AvailableRoles.Add(role);
        }
    }

    [RelayCommand]
    private async Task LoadUsersAsync()
    {
        await ExecuteAsync(async () =>
        {
            var users = await _supabaseService.GetUsersAsync();
            
            Users.Clear();
            foreach (var user in users)
            {
                Users.Add(user);
            }
            
            _logger.LogInformation("Loaded {Count} users", Users.Count);
        });
    }

    [RelayCommand]
    private void StartAddNew()
    {
        IsAddingNew = true;
        NewUserEmail = string.Empty;
        NewUserFullName = string.Empty;
        NewUserRole = UserRole.Viewer;
    }

    [RelayCommand]
    private void CancelAdd()
    {
        IsAddingNew = false;
        NewUserEmail = string.Empty;
        NewUserFullName = string.Empty;
        NewUserRole = UserRole.Viewer;
    }

    [RelayCommand]
    private async Task InviteUserAsync()
    {
        if (string.IsNullOrWhiteSpace(NewUserEmail) || string.IsNullOrWhiteSpace(NewUserFullName))
        {
            ShowError("Email and full name are required.");
            return;
        }

        await ExecuteAsync(async () =>
        {
            // TODO: Implement user invitation logic
            // This would typically involve sending an invitation email
            // and creating a pending user record
            
            ShowSuccess($"Invitation sent to {NewUserEmail}");
            CancelAdd();
        });
    }

    [RelayCommand]
    private async Task UpdateUserRoleAsync(UserProfile? user, UserRole newRole)
    {
        if (user == null) return;

        await ExecuteAsync(async () =>
        {
            user.Role = newRole;
            await _supabaseService.UpdateUserAsync(user);
            ShowSuccess($"Updated role for {user.FullName ?? user.Email} to {newRole}");
        });
    }

    [RelayCommand]
    private async Task DeactivateUserAsync(UserProfile? user)
    {
        if (user == null) return;

        var result = System.Windows.MessageBox.Show(
            $"Are you sure you want to deactivate user '{user.FullName ?? user.Email}'?",
            "Confirm Deactivation",
            System.Windows.MessageBoxButton.YesNo,
            System.Windows.MessageBoxImage.Warning);

        if (result == System.Windows.MessageBoxResult.Yes)
        {
            await ExecuteAsync(async () =>
            {
                // TODO: Implement user deactivation logic
                ShowSuccess($"User {user.FullName ?? user.Email} deactivated successfully.");
            });
        }
    }

    [RelayCommand]
    private async Task ResetPasswordAsync(UserProfile? user)
    {
        if (user == null) return;

        await ExecuteAsync(async () =>
        {
            // TODO: Implement password reset logic
            ShowSuccess($"Password reset email sent to {user.Email}");
        });
    }

    private void LoadUsers()
    {
        _ = LoadUsersAsync();
    }
}
