using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace MusicEntertainmentApp.Models;

public class Bundle : BaseEntity
{
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    public int MaxTracksPerBundle { get; set; } = 150;
    
    public int MaxTracksPerArtist { get; set; } = 1;
    
    public bool AutoAssign { get; set; } = false;
    
    // Navigation properties
    public virtual UserProfile? Creator { get; set; }
    public virtual ICollection<BundleTrack> BundleTracks { get; set; } = new List<BundleTrack>();
    
    // Computed properties
    public int TrackCount => BundleTracks?.Count ?? 0;
    public int AvailableSlots => MaxTracksPerBundle - TrackCount;
    public bool IsFull => TrackCount >= MaxTracksPerBundle;
}

public class BundleTrack : BaseEntity
{
    public Guid BundleId { get; set; }
    
    public Guid TrackId { get; set; }
    
    public DateTime AssignedDate { get; set; } = DateTime.UtcNow;
    
    public bool IsAutoAssigned { get; set; } = false;
    
    // Navigation properties
    public virtual Bundle? Bundle { get; set; }
    public virtual Track? Track { get; set; }
}
