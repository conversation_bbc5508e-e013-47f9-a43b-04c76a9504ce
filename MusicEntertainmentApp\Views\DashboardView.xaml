<UserControl x:Class="MusicEntertainmentApp.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="Releases Dashboard" 
                       FontSize="28" 
                       FontWeight="Bold" 
                       Margin="0,0,0,10"/>
            <TextBlock Text="View and manage music releases" 
                       FontSize="14" 
                       Opacity="0.7"/>
        </StackPanel>

        <!-- Filters and Actions -->
        <materialDesign:Card Grid.Row="1" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Filter Controls -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Search -->
                    <TextBox Grid.Column="0"
                             Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="Search releases..."
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,0,10,0"/>

                    <!-- Status Filter -->
                    <ComboBox Grid.Column="1"
                              ItemsSource="{Binding AvailableStatuses}"
                              SelectedItem="{Binding SelectedStatus}"
                              materialDesign:HintAssist.Hint="Status"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              Margin="0,0,10,0"/>

                    <!-- Artist Filter -->
                    <ComboBox Grid.Column="2"
                              ItemsSource="{Binding AvailableArtists}"
                              SelectedItem="{Binding SelectedArtist}"
                              materialDesign:HintAssist.Hint="Artist"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              Margin="0,0,10,0"/>

                    <!-- Genre Filter -->
                    <ComboBox Grid.Column="3"
                              ItemsSource="{Binding AvailableGenres}"
                              SelectedItem="{Binding SelectedGenre}"
                              materialDesign:HintAssist.Hint="Genre"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              Margin="0,0,10,0"/>

                    <!-- Clear Filters -->
                    <Button Grid.Column="4"
                            Command="{Binding ClearFiltersCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="Clear"
                            ToolTip="Clear all filters"/>
                </Grid>

                <!-- Action Buttons -->
                <StackPanel Grid.Row="1" 
                            Orientation="Horizontal" 
                            HorizontalAlignment="Right"
                            Margin="0,15,0,0">
                    
                    <CheckBox Content="Show/Hide all Titles inline"
                              IsChecked="{Binding ShowAllTitles}"
                              Margin="0,0,20,0"
                              VerticalAlignment="Center"/>

                    <Button Command="{Binding ExportToExcelCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Content="CSV/XLSX"
                            Margin="0,0,10,0">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileExcel" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                <TextBlock Text="Export"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Button Command="{Binding LoadDataCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            ToolTip="Refresh data">
                        <materialDesign:PackIcon Kind="Refresh"/>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Data Grid -->
        <materialDesign:Card Grid.Row="2">
            <Grid>
                <DataGrid ItemsSource="{Binding FilteredReleases}"
                          SelectedItem="{Binding SelectedRelease}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="False"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          materialDesign:DataGridAssist.CellPadding="8"
                          materialDesign:DataGridAssist.ColumnHeaderPadding="8">

                    <DataGrid.Columns>
                        <!-- Row Number -->
                        <DataGridTextColumn Header="#" 
                                            Binding="{Binding RelativeSource={RelativeSource AncestorType=DataGridRow}, Path=Header}"
                                            Width="50"
                                            IsReadOnly="True"/>

                        <!-- Artist -->
                        <DataGridTextColumn Header="Artist" 
                                            Binding="{Binding ArtistName}"
                                            Width="150"/>

                        <!-- Label -->
                        <DataGridTextColumn Header="Label" 
                                            Binding="{Binding LabelName}"
                                            Width="120"/>

                        <!-- Distributor -->
                        <DataGridTextColumn Header="Distributor" 
                                            Binding="{Binding LabelName}"
                                            Width="120"/>

                        <!-- Release Date -->
                        <DataGridTextColumn Header="Release Date" 
                                            Binding="{Binding ReleaseDate, StringFormat=dd/MM/yyyy}"
                                            Width="100"/>

                        <!-- Release Title -->
                        <DataGridTextColumn Header="Release" 
                                            Binding="{Binding Title}"
                                            Width="200"/>

                        <!-- Bundle -->
                        <DataGridTextColumn Header="Bundle" 
                                            Binding="{Binding CatalogNumber}"
                                            Width="100"/>

                        <!-- Playlist Count -->
                        <DataGridTextColumn Header="Playlist Count" 
                                            Binding="{Binding TrackCount}"
                                            Width="100"/>

                        <!-- Status -->
                        <DataGridTemplateColumn Header="Status" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="{Binding Status, Converter={StaticResource StatusToBrushConverter}}"
                                            CornerRadius="12"
                                            Padding="8,4">
                                        <TextBlock Text="{Binding Status}" 
                                                   Foreground="White"
                                                   FontSize="10"
                                                   FontWeight="Bold"
                                                   HorizontalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Notes -->
                        <DataGridTextColumn Header="Notes" 
                                            Binding="{Binding Notes}"
                                            Width="150"/>

                        <!-- Actions -->
                        <DataGridTemplateColumn Header="Actions" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Command="{Binding DataContext.EditReleaseCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource MaterialDesignIconButton}"
                                                ToolTip="Edit">
                                            <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                        </Button>
                                        <Button Command="{Binding DataContext.DeleteReleaseCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource MaterialDesignIconButton}"
                                                ToolTip="Delete">
                                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Loading Overlay -->
                <Grid Background="White" 
                      Opacity="0.8"
                      Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}}">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <ProgressBar IsIndeterminate="True" Width="100" Height="4" Margin="0,0,0,10"/>
                        <TextBlock Text="Loading releases..." HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>

                <!-- No Data Message -->
                <StackPanel HorizontalAlignment="Center" 
                            VerticalAlignment="Center"
                            Visibility="{Binding FilteredReleases.Count, Converter={StaticResource CountToVisibilityConverter}, ConverterParameter=Inverse}">
                    <materialDesign:PackIcon Kind="MusicNoteOff" Width="64" Height="64" Opacity="0.3"/>
                    <TextBlock Text="No releases found" 
                               FontSize="16" 
                               Margin="0,10,0,0"
                               HorizontalAlignment="Center"
                               Opacity="0.6"/>
                    <TextBlock Text="Try adjusting your filters or add some releases" 
                               FontSize="12" 
                               HorizontalAlignment="Center"
                               Opacity="0.4"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Pagination and Status -->
        <Grid Grid.Row="3" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Status Info -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Text="{Binding FilteredCount, StringFormat='Showing {0} releases'}" 
                           Margin="0,0,10,0"/>
                <TextBlock Text="{Binding TotalReleases, StringFormat='of {0} total'}" 
                           Opacity="0.6"/>
            </StackPanel>

            <!-- Pagination -->
            <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                <Button Command="{Binding PreviousPageCommand}"
                        Style="{StaticResource MaterialDesignIconButton}"
                        IsEnabled="{Binding CanGoPrevious}">
                    <materialDesign:PackIcon Kind="ChevronLeft"/>
                </Button>
                
                <TextBlock Text="{Binding CurrentPage, StringFormat='Page {0}'}" 
                           VerticalAlignment="Center"
                           Margin="10,0"/>
                
                <TextBlock Text="{Binding TotalPages, StringFormat='of {0}'}" 
                           VerticalAlignment="Center"
                           Margin="0,0,10,0"
                           Opacity="0.6"/>
                
                <Button Command="{Binding NextPageCommand}"
                        Style="{StaticResource MaterialDesignIconButton}"
                        IsEnabled="{Binding CanGoNext}">
                    <materialDesign:PackIcon Kind="ChevronRight"/>
                </Button>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
