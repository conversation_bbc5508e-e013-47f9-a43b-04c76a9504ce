using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MusicEntertainmentApp.Models;
using MusicEntertainmentApp.Services;
using System.Collections.ObjectModel;

namespace MusicEntertainmentApp.ViewModels;

public partial class BundlesViewModel : BaseViewModel
{
    [ObservableProperty]
    private ObservableCollection<Bundle> _bundles = new();

    [ObservableProperty]
    private Bundle? _selectedBundle;

    [ObservableProperty]
    private int _maxTracksPerBundle = 150;

    [ObservableProperty]
    private int _maxTracksPerArtist = 1;

    public BundlesViewModel(ISupabaseService supabaseService, ILogger<BundlesViewModel> logger)
        : base(supabaseService, logger)
    {
        LoadBundles();
    }

    [RelayCommand]
    private async Task LoadBundlesAsync()
    {
        await ExecuteAsync(async () =>
        {
            var bundles = await _supabaseService.GetBundlesAsync();
            
            Bundles.Clear();
            foreach (var bundle in bundles)
            {
                Bundles.Add(bundle);
            }
            
            _logger.LogInformation("Loaded {Count} bundles", Bundles.Count);
        });
    }

    [RelayCommand]
    private async Task AutoDistributeTracksAsync()
    {
        await ExecuteAsync(async () =>
        {
            // TODO: Implement auto-distribution logic
            ShowSuccess("Auto-distribution functionality will be implemented soon.");
        });
    }

    private void LoadBundles()
    {
        _ = LoadBundlesAsync();
    }
}
