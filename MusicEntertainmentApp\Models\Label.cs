using System.ComponentModel.DataAnnotations;

namespace MusicEntertainmentApp.Models;

public class Label : BaseEntity
{
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;
    
    [EmailAddress]
    public string? ContactEmail { get; set; }
    
    public string? ContactPhone { get; set; }
    
    public string? Website { get; set; }
    
    // Navigation properties
    public virtual ICollection<Release> Releases { get; set; } = new List<Release>();
}
