using System.ComponentModel.DataAnnotations;

namespace MusicEntertainmentApp.Models;

public class FileEntity : BaseEntity
{
    [Required]
    [StringLength(500)]
    public string FileName { get; set; } = string.Empty;
    
    [Required]
    public string FilePath { get; set; } = string.Empty;
    
    public long? FileSize { get; set; }
    
    public string? MimeType { get; set; }
    
    public string? EntityType { get; set; } // 'release', 'track', 'artist', etc.
    
    public Guid? EntityId { get; set; }
    
    public Guid? UploadedBy { get; set; }
    
    // Navigation properties
    public virtual UserProfile? Uploader { get; set; }
    
    // Computed properties
    public string FileSizeFormatted
    {
        get
        {
            if (!FileSize.HasValue) return "Unknown";
            
            var size = FileSize.Value;
            string[] sizes = { "B", "KB", "MB", "GB" };
            int order = 0;
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size = size / 1024;
            }
            return $"{size:0.##} {sizes[order]}";
        }
    }
    
    public string FileExtension => Path.GetExtension(FileName).ToLowerInvariant();
    
    public bool IsImage => new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp" }.Contains(FileExtension);
    
    public bool IsAudio => new[] { ".mp3", ".wav", ".flac", ".m4a", ".aac" }.Contains(FileExtension);
    
    public bool IsDocument => new[] { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt" }.Contains(FileExtension);
}
