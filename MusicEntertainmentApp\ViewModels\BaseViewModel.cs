using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MusicEntertainmentApp.Services;

namespace MusicEntertainmentApp.ViewModels;

public abstract partial class BaseViewModel : ObservableObject
{
    protected readonly ISupabaseService _supabaseService;
    protected readonly ILogger _logger;

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    private string _errorMessage = string.Empty;

    [ObservableProperty]
    private string _successMessage = string.Empty;

    protected BaseViewModel(ISupabaseService supabaseService, ILogger logger)
    {
        _supabaseService = supabaseService;
        _logger = logger;
    }

    protected void ClearMessages()
    {
        ErrorMessage = string.Empty;
        SuccessMessage = string.Empty;
    }

    protected void ShowError(string message)
    {
        ErrorMessage = message;
        SuccessMessage = string.Empty;
    }

    protected void ShowSuccess(string message)
    {
        SuccessMessage = message;
        ErrorMessage = string.Empty;
    }

    protected async Task ExecuteAsync(Func<Task> operation, string? loadingMessage = null)
    {
        try
        {
            ClearMessages();
            IsLoading = true;
            
            await operation();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Operation failed");
            ShowError($"Operation failed: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> operation, string? loadingMessage = null)
    {
        try
        {
            ClearMessages();
            IsLoading = true;
            
            return await operation();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Operation failed");
            ShowError($"Operation failed: {ex.Message}");
            return default;
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    protected virtual void ClearError()
    {
        ErrorMessage = string.Empty;
    }

    [RelayCommand]
    protected virtual void ClearSuccess()
    {
        SuccessMessage = string.Empty;
    }
}
