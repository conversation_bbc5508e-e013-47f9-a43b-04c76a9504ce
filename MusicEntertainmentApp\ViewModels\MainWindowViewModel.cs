using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MusicEntertainmentApp.Models;
using MusicEntertainmentApp.Services;
using System.Collections.ObjectModel;

namespace MusicEntertainmentApp.ViewModels;

public partial class MainWindowViewModel : BaseViewModel
{
    [ObservableProperty]
    private UserProfile? _currentUser;

    [ObservableProperty]
    private string _selectedTab = "Dashboard";

    [ObservableProperty]
    private bool _isDarkTheme = true;

    [ObservableProperty]
    private bool _isSidebarExpanded = true;

    [ObservableProperty]
    private string _searchText = string.Empty;

    public ObservableCollection<NavigationItem> NavigationItems { get; } = new();

    public MainWindowViewModel(ISupabaseService supabaseService, ILogger<MainWindowViewModel> logger)
        : base(supabaseService, logger)
    {
        InitializeNavigationItems();
        LoadCurrentUser();
    }

    private void InitializeNavigationItems()
    {
        NavigationItems.Clear();
        NavigationItems.Add(new NavigationItem("Dashboard", "ViewDashboard", "📊"));
        NavigationItems.Add(new NavigationItem("Releases", "Releases", "🎵"));
        NavigationItems.Add(new NavigationItem("Artists", "Artists", "👤"));
        NavigationItems.Add(new NavigationItem("Playlists", "Playlists", "📋"));
        NavigationItems.Add(new NavigationItem("Bundles", "Bundles", "📦"));
        NavigationItems.Add(new NavigationItem("Labels", "Labels", "🏷️"));
        NavigationItems.Add(new NavigationItem("Analytics", "Analytics", "📈"));
        NavigationItems.Add(new NavigationItem("Audio Library", "AudioLibrary", "🎧"));
        NavigationItems.Add(new NavigationItem("Files", "Files", "📁"));
        NavigationItems.Add(new NavigationItem("Service Accounts", "ServiceAccounts", "🔗"));
        NavigationItems.Add(new NavigationItem("Company Vault", "CompanyVault", "🏛️"));
        NavigationItems.Add(new NavigationItem("Risk Analysis", "RiskAnalysis", "⚠️"));
        NavigationItems.Add(new NavigationItem("Calendar View", "CalendarView", "📅"));
    }

    private async void LoadCurrentUser()
    {
        await ExecuteAsync(async () =>
        {
            CurrentUser = await _supabaseService.GetCurrentUserProfileAsync();
            
            // Update navigation based on user role
            UpdateNavigationForUserRole();
        });
    }

    private void UpdateNavigationForUserRole()
    {
        if (CurrentUser == null) return;

        // Add admin-only items
        if (CurrentUser.Role == UserRole.Admin)
        {
            if (!NavigationItems.Any(x => x.Name == "User Management"))
            {
                NavigationItems.Add(new NavigationItem("User Management", "UserManagement", "👥"));
                NavigationItems.Add(new NavigationItem("Settings", "Settings", "⚙️"));
            }
        }
        else
        {
            // Remove admin-only items for non-admin users
            var adminItems = NavigationItems.Where(x => x.Name is "User Management" or "Settings").ToList();
            foreach (var item in adminItems)
            {
                NavigationItems.Remove(item);
            }
        }
    }

    [RelayCommand]
    private void NavigateToTab(string tabName)
    {
        SelectedTab = tabName;
        _logger.LogInformation("Navigated to tab: {TabName}", tabName);
    }

    [RelayCommand]
    private void ToggleSidebar()
    {
        IsSidebarExpanded = !IsSidebarExpanded;
    }

    [RelayCommand]
    private void ToggleTheme()
    {
        IsDarkTheme = !IsDarkTheme;
        // TODO: Apply theme changes to the application
        _logger.LogInformation("Theme changed to: {Theme}", IsDarkTheme ? "Dark" : "Light");
    }

    [RelayCommand]
    private async Task SignOutAsync()
    {
        await ExecuteAsync(async () =>
        {
            await _supabaseService.SignOutAsync();
            CurrentUser = null;
            
            // Show login window
            var loginWindow = new Views.LoginWindow();
            loginWindow.Show();
            
            // Close main window
            System.Windows.Application.Current.MainWindow?.Close();
        });
    }

    [RelayCommand]
    private void ShowUserProfile()
    {
        // TODO: Implement user profile dialog
        ShowSuccess("User profile dialog will be implemented soon.");
    }

    [RelayCommand]
    private void GlobalSearch()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            ShowError("Please enter a search term.");
            return;
        }

        // TODO: Implement global search functionality
        ShowSuccess($"Searching for: {SearchText}");
        _logger.LogInformation("Global search performed: {SearchText}", SearchText);
    }

    partial void OnSelectedTabChanged(string value)
    {
        _logger.LogInformation("Selected tab changed to: {Tab}", value);
    }
}

public class NavigationItem
{
    public string Name { get; }
    public string Route { get; }
    public string Icon { get; }

    public NavigationItem(string name, string route, string icon)
    {
        Name = name;
        Route = route;
        Icon = icon;
    }
}
