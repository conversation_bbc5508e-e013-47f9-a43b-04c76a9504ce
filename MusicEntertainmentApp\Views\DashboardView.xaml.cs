using System.Windows.Controls;
using MusicEntertainmentApp.ViewModels;

namespace MusicEntertainmentApp.Views;

/// <summary>
/// Interaction logic for DashboardView.xaml
/// </summary>
public partial class DashboardView : UserControl
{
    public DashboardView(DashboardViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
    }

    public DashboardView() : this(null!)
    {
        // Parameterless constructor for XAML designer
    }
}
