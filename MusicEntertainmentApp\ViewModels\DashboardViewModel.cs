using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MusicEntertainmentApp.Models;
using MusicEntertainmentApp.Services;
using System.Collections.ObjectModel;

namespace MusicEntertainmentApp.ViewModels;

public partial class DashboardViewModel : BaseViewModel
{
    private readonly IExcelExportService _excelExportService;

    [ObservableProperty]
    private ObservableCollection<Release> _releases = new();

    [ObservableProperty]
    private ObservableCollection<Release> _filteredReleases = new();

    [ObservableProperty]
    private string _searchText = string.Empty;

    [ObservableProperty]
    private ReleaseStatus? _selectedStatus;

    [ObservableProperty]
    private string _selectedArtist = string.Empty;

    [ObservableProperty]
    private string _selectedGenre = string.Empty;

    [ObservableProperty]
    private bool _showAllTitles = true;

    [ObservableProperty]
    private int _totalReleases;

    [ObservableProperty]
    private int _filteredCount;

    [ObservableProperty]
    private int _currentPage = 1;

    [ObservableProperty]
    private int _pageSize = 50;

    [ObservableProperty]
    private int _totalPages = 1;

    [ObservableProperty]
    private Release? _selectedRelease;

    public ObservableCollection<string> AvailableArtists { get; } = new();
    public ObservableCollection<string> AvailableGenres { get; } = new();
    public ObservableCollection<ReleaseStatus> AvailableStatuses { get; } = new();

    public DashboardViewModel(ISupabaseService supabaseService, IExcelExportService excelExportService, ILogger<DashboardViewModel> logger)
        : base(supabaseService, logger)
    {
        _excelExportService = excelExportService;
        InitializeFilters();
        LoadData();
    }

    private void InitializeFilters()
    {
        foreach (var status in Enum.GetValues<ReleaseStatus>())
        {
            AvailableStatuses.Add(status);
        }
    }

    [RelayCommand]
    private async Task LoadDataAsync()
    {
        await ExecuteAsync(async () =>
        {
            var releases = await _supabaseService.GetReleasesAsync(CurrentPage, PageSize);
            
            Releases.Clear();
            foreach (var release in releases)
            {
                Releases.Add(release);
            }

            // Update filter options
            UpdateFilterOptions();
            
            // Apply current filters
            ApplyFilters();
            
            TotalReleases = Releases.Count;
            
            _logger.LogInformation("Loaded {Count} releases", Releases.Count);
        });
    }

    private void UpdateFilterOptions()
    {
        // Update available artists
        var artists = Releases.Select(r => r.ArtistName).Distinct().OrderBy(a => a).ToList();
        AvailableArtists.Clear();
        AvailableArtists.Add("All Artists");
        foreach (var artist in artists)
        {
            AvailableArtists.Add(artist);
        }

        // Update available genres
        var genres = Releases.Where(r => !string.IsNullOrEmpty(r.Genre))
                            .Select(r => r.Genre!)
                            .Distinct()
                            .OrderBy(g => g)
                            .ToList();
        AvailableGenres.Clear();
        AvailableGenres.Add("All Genres");
        foreach (var genre in genres)
        {
            AvailableGenres.Add(genre);
        }
    }

    [RelayCommand]
    private void ApplyFilters()
    {
        var filtered = Releases.AsEnumerable();

        // Search filter
        if (!string.IsNullOrWhiteSpace(SearchText))
        {
            filtered = filtered.Where(r => 
                r.Title.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                r.ArtistName.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                r.LabelName.Contains(SearchText, StringComparison.OrdinalIgnoreCase));
        }

        // Status filter
        if (SelectedStatus.HasValue)
        {
            filtered = filtered.Where(r => r.Status == SelectedStatus.Value);
        }

        // Artist filter
        if (!string.IsNullOrEmpty(SelectedArtist) && SelectedArtist != "All Artists")
        {
            filtered = filtered.Where(r => r.ArtistName == SelectedArtist);
        }

        // Genre filter
        if (!string.IsNullOrEmpty(SelectedGenre) && SelectedGenre != "All Genres")
        {
            filtered = filtered.Where(r => r.Genre == SelectedGenre);
        }

        FilteredReleases.Clear();
        foreach (var release in filtered)
        {
            FilteredReleases.Add(release);
        }

        FilteredCount = FilteredReleases.Count;
        UpdatePagination();
    }

    [RelayCommand]
    private void ClearFilters()
    {
        SearchText = string.Empty;
        SelectedStatus = null;
        SelectedArtist = "All Artists";
        SelectedGenre = "All Genres";
        ApplyFilters();
    }

    [RelayCommand]
    private async Task ExportToExcelAsync()
    {
        await ExecuteAsync(async () =>
        {
            var releasesToExport = FilteredReleases.Any() ? FilteredReleases : Releases;
            var filePath = await _excelExportService.ExportReleasesAsync(releasesToExport);
            
            ShowSuccess($"Exported {releasesToExport.Count()} releases to {Path.GetFileName(filePath)}");
            
            // Open the file location
            System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{filePath}\"");
        });
    }

    [RelayCommand]
    private void EditRelease(Release? release)
    {
        if (release == null) return;
        
        SelectedRelease = release;
        // TODO: Open edit dialog
        ShowSuccess($"Edit dialog for '{release.Title}' will be implemented soon.");
    }

    [RelayCommand]
    private async Task DeleteReleaseAsync(Release? release)
    {
        if (release == null) return;

        // TODO: Show confirmation dialog
        var result = System.Windows.MessageBox.Show(
            $"Are you sure you want to delete the release '{release.Title}'?",
            "Confirm Delete",
            System.Windows.MessageBoxButton.YesNo,
            System.Windows.MessageBoxImage.Warning);

        if (result == System.Windows.MessageBoxResult.Yes)
        {
            await ExecuteAsync(async () =>
            {
                await _supabaseService.DeleteReleaseAsync(release.Id);
                Releases.Remove(release);
                FilteredReleases.Remove(release);
                ShowSuccess($"Release '{release.Title}' deleted successfully.");
            });
        }
    }

    [RelayCommand]
    private async Task NextPageAsync()
    {
        if (CurrentPage < TotalPages)
        {
            CurrentPage++;
            await LoadDataAsync();
        }
    }

    [RelayCommand]
    private async Task PreviousPageAsync()
    {
        if (CurrentPage > 1)
        {
            CurrentPage--;
            await LoadDataAsync();
        }
    }

    [RelayCommand]
    private async Task GoToPageAsync(int page)
    {
        if (page >= 1 && page <= TotalPages && page != CurrentPage)
        {
            CurrentPage = page;
            await LoadDataAsync();
        }
    }

    private void UpdatePagination()
    {
        TotalPages = (int)Math.Ceiling((double)FilteredCount / PageSize);
        if (CurrentPage > TotalPages)
        {
            CurrentPage = Math.Max(1, TotalPages);
        }
    }

    private void LoadData()
    {
        _ = LoadDataAsync();
    }

    partial void OnSearchTextChanged(string value)
    {
        ApplyFilters();
    }

    partial void OnSelectedStatusChanged(ReleaseStatus? value)
    {
        ApplyFilters();
    }

    partial void OnSelectedArtistChanged(string value)
    {
        ApplyFilters();
    }

    partial void OnSelectedGenreChanged(string value)
    {
        ApplyFilters();
    }

    partial void OnPageSizeChanged(int value)
    {
        UpdatePagination();
        _ = LoadDataAsync();
    }
}
