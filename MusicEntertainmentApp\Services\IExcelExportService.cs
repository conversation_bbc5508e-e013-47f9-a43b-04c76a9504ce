using MusicEntertainmentApp.Models;

namespace MusicEntertainmentApp.Services;

public interface IExcelExportService
{
    Task<string> ExportReleasesAsync(IEnumerable<Release> releases, string? filePath = null);
    Task<string> ExportArtistsAsync(IEnumerable<Artist> artists, string? filePath = null);
    Task<string> ExportTracksAsync(IEnumerable<Track> tracks, string? filePath = null);
    Task<string> ExportPlaylistsAsync(IEnumerable<Playlist> playlists, string? filePath = null);
    Task<string> ExportBundlesAsync(IEnumerable<Bundle> bundles, string? filePath = null);
    Task<string> ExportPlaylistDistributionAsync(IEnumerable<Playlist> playlists, IEnumerable<Track> tracks, string? filePath = null);
    Task<string> ExportDashboardDataAsync(IEnumerable<Release> releases, Dictionary<string, int> stats, string? filePath = null);
}

public class ExcelExportService : IExcelExportService
{
    private readonly ILogger<ExcelExportService> _logger;
    private readonly IConfiguration _configuration;

    public ExcelExportService(ILogger<ExcelExportService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<string> ExportReleasesAsync(IEnumerable<Release> releases, string? filePath = null)
    {
        try
        {
            filePath ??= GetDefaultFilePath("releases");
            
            using var package = new OfficeOpenXml.ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Releases");

            // Headers
            var headers = new[]
            {
                "ID", "Title", "Artist", "Label", "Release Date", "Status", 
                "Genre", "UPC Code", "Catalog Number", "Track Count", "Notes", 
                "Created At", "Updated At"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Data
            int row = 2;
            foreach (var release in releases)
            {
                worksheet.Cells[row, 1].Value = release.Id.ToString();
                worksheet.Cells[row, 2].Value = release.Title;
                worksheet.Cells[row, 3].Value = release.ArtistName;
                worksheet.Cells[row, 4].Value = release.LabelName;
                worksheet.Cells[row, 5].Value = release.ReleaseDate?.ToString("yyyy-MM-dd");
                worksheet.Cells[row, 6].Value = release.Status.ToString();
                worksheet.Cells[row, 7].Value = release.Genre;
                worksheet.Cells[row, 8].Value = release.UpcCode;
                worksheet.Cells[row, 9].Value = release.CatalogNumber;
                worksheet.Cells[row, 10].Value = release.TrackCount;
                worksheet.Cells[row, 11].Value = release.Notes;
                worksheet.Cells[row, 12].Value = release.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss");
                worksheet.Cells[row, 13].Value = release.UpdatedAt.ToString("yyyy-MM-dd HH:mm:ss");
                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            // Apply styling
            ApplyTableStyling(worksheet, headers.Length, row - 1);

            await package.SaveAsAsync(new FileInfo(filePath));
            _logger.LogInformation("Exported {Count} releases to {FilePath}", releases.Count(), filePath);
            
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export releases");
            throw;
        }
    }

    public async Task<string> ExportArtistsAsync(IEnumerable<Artist> artists, string? filePath = null)
    {
        try
        {
            filePath ??= GetDefaultFilePath("artists");
            
            using var package = new OfficeOpenXml.ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Artists");

            // Headers
            var headers = new[]
            {
                "ID", "Name", "Bio", "Spotify ID", "Apple Music ID", 
                "YouTube Channel ID", "Created At", "Updated At"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Data
            int row = 2;
            foreach (var artist in artists)
            {
                worksheet.Cells[row, 1].Value = artist.Id.ToString();
                worksheet.Cells[row, 2].Value = artist.Name;
                worksheet.Cells[row, 3].Value = artist.Bio;
                worksheet.Cells[row, 4].Value = artist.SpotifyId;
                worksheet.Cells[row, 5].Value = artist.AppleMusicId;
                worksheet.Cells[row, 6].Value = artist.YouTubeChannelId;
                worksheet.Cells[row, 7].Value = artist.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss");
                worksheet.Cells[row, 8].Value = artist.UpdatedAt.ToString("yyyy-MM-dd HH:mm:ss");
                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            // Apply styling
            ApplyTableStyling(worksheet, headers.Length, row - 1);

            await package.SaveAsAsync(new FileInfo(filePath));
            _logger.LogInformation("Exported {Count} artists to {FilePath}", artists.Count(), filePath);
            
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export artists");
            throw;
        }
    }

    public async Task<string> ExportTracksAsync(IEnumerable<Track> tracks, string? filePath = null)
    {
        try
        {
            filePath ??= GetDefaultFilePath("tracks");
            
            using var package = new OfficeOpenXml.ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Tracks");

            // Headers
            var headers = new[]
            {
                "ID", "Title", "Artist", "Release", "Track Number", "Duration", 
                "ISRC Code", "Status", "Tags", "Created At", "Updated At"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Data
            int row = 2;
            foreach (var track in tracks)
            {
                worksheet.Cells[row, 1].Value = track.Id.ToString();
                worksheet.Cells[row, 2].Value = track.Title;
                worksheet.Cells[row, 3].Value = track.ArtistName;
                worksheet.Cells[row, 4].Value = track.ReleaseName;
                worksheet.Cells[row, 5].Value = track.TrackNumber;
                worksheet.Cells[row, 6].Value = track.DurationFormatted;
                worksheet.Cells[row, 7].Value = track.IsrcCode;
                worksheet.Cells[row, 8].Value = track.Status.ToString();
                worksheet.Cells[row, 9].Value = track.TagsDisplay;
                worksheet.Cells[row, 10].Value = track.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss");
                worksheet.Cells[row, 11].Value = track.UpdatedAt.ToString("yyyy-MM-dd HH:mm:ss");
                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            // Apply styling
            ApplyTableStyling(worksheet, headers.Length, row - 1);

            await package.SaveAsAsync(new FileInfo(filePath));
            _logger.LogInformation("Exported {Count} tracks to {FilePath}", tracks.Count(), filePath);
            
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export tracks");
            throw;
        }
    }

    public Task<string> ExportPlaylistsAsync(IEnumerable<Playlist> playlists, string? filePath = null)
    {
        // Implementation similar to above methods
        throw new NotImplementedException();
    }

    public Task<string> ExportBundlesAsync(IEnumerable<Bundle> bundles, string? filePath = null)
    {
        // Implementation similar to above methods
        throw new NotImplementedException();
    }

    public Task<string> ExportPlaylistDistributionAsync(IEnumerable<Playlist> playlists, IEnumerable<Track> tracks, string? filePath = null)
    {
        // Implementation for playlist distribution export
        throw new NotImplementedException();
    }

    public Task<string> ExportDashboardDataAsync(IEnumerable<Release> releases, Dictionary<string, int> stats, string? filePath = null)
    {
        // Implementation for dashboard data export
        throw new NotImplementedException();
    }

    private string GetDefaultFilePath(string entityType)
    {
        var defaultPath = _configuration["Export:DefaultExportPath"] ?? Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
        defaultPath = Environment.ExpandEnvironmentVariables(defaultPath);
        
        var fileName = $"{entityType}_export_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
        return Path.Combine(defaultPath, fileName);
    }

    private static void ApplyTableStyling(OfficeOpenXml.ExcelWorksheet worksheet, int columnCount, int rowCount)
    {
        // Header styling
        using (var range = worksheet.Cells[1, 1, 1, columnCount])
        {
            range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
            range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.FromArgb(79, 129, 189));
            range.Style.Font.Color.SetColor(System.Drawing.Color.White);
            range.Style.Font.Bold = true;
        }

        // Alternate row coloring
        for (int row = 2; row <= rowCount; row++)
        {
            if (row % 2 == 0)
            {
                using var range = worksheet.Cells[row, 1, row, columnCount];
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.FromArgb(242, 242, 242));
            }
        }

        // Borders
        using (var range = worksheet.Cells[1, 1, rowCount, columnCount])
        {
            range.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        }
    }
}
