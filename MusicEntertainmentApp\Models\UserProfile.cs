using System.ComponentModel.DataAnnotations;

namespace MusicEntertainmentApp.Models;

public class UserProfile : BaseEntity
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    public string? FullName { get; set; }
    
    public UserRole Role { get; set; } = UserRole.Viewer;
    
    public string? AvatarUrl { get; set; }
    
    // Navigation properties
    public virtual ICollection<Artist> CreatedArtists { get; set; } = new List<Artist>();
    public virtual ICollection<Release> CreatedReleases { get; set; } = new List<Release>();
    public virtual ICollection<Track> CreatedTracks { get; set; } = new List<Track>();
    public virtual ICollection<Playlist> CreatedPlaylists { get; set; } = new List<Playlist>();
    public virtual ICollection<Bundle> CreatedBundles { get; set; } = new List<Bundle>();
    public virtual ICollection<AppSetting> Settings { get; set; } = new List<AppSetting>();
}
