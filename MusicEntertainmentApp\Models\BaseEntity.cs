using System.ComponentModel.DataAnnotations;

namespace MusicEntertainmentApp.Models;

public abstract class BaseEntity
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public Guid? CreatedBy { get; set; }
}

public enum UserRole
{
    Viewer,
    Editor,
    Admin
}

public enum ReleaseStatus
{
    Draft,
    Scheduled,
    Released,
    Archived
}

public enum PlaylistPlatform
{
    Spotify,
    YouTube,
    AppleMusic,
    AmazonMusic,
    Other
}

public enum TrackStatus
{
    Active,
    Inactive,
    Pending
}
