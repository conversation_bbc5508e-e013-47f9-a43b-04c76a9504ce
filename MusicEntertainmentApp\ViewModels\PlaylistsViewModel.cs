using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MusicEntertainmentApp.Models;
using MusicEntertainmentApp.Services;
using System.Collections.ObjectModel;

namespace MusicEntertainmentApp.ViewModels;

public partial class PlaylistsViewModel : BaseViewModel
{
    [ObservableProperty]
    private ObservableCollection<Playlist> _playlists = new();

    [ObservableProperty]
    private string _searchText = string.Empty;

    [ObservableProperty]
    private PlaylistPlatform? _selectedPlatform;

    [ObservableProperty]
    private Playlist? _selectedPlaylist;

    public ObservableCollection<PlaylistPlatform> AvailablePlatforms { get; } = new();

    public PlaylistsViewModel(ISupabaseService supabaseService, ILogger<PlaylistsViewModel> logger)
        : base(supabaseService, logger)
    {
        InitializePlatforms();
        LoadPlaylists();
    }

    private void InitializePlatforms()
    {
        foreach (var platform in Enum.GetValues<PlaylistPlatform>())
        {
            AvailablePlatforms.Add(platform);
        }
    }

    [RelayCommand]
    private async Task LoadPlaylistsAsync()
    {
        await ExecuteAsync(async () =>
        {
            var playlists = await _supabaseService.GetPlaylistsAsync(search: SearchText, platform: SelectedPlatform);
            
            Playlists.Clear();
            foreach (var playlist in playlists)
            {
                Playlists.Add(playlist);
            }
            
            _logger.LogInformation("Loaded {Count} playlists", Playlists.Count);
        });
    }

    [RelayCommand]
    private void ViewPlaylistDetails(Playlist? playlist)
    {
        if (playlist == null) return;
        
        SelectedPlaylist = playlist;
        ShowSuccess($"Playlist details for '{playlist.Name}' will be implemented soon.");
    }

    private void LoadPlaylists()
    {
        _ = LoadPlaylistsAsync();
    }

    partial void OnSearchTextChanged(string value)
    {
        _ = LoadPlaylistsAsync();
    }

    partial void OnSelectedPlatformChanged(PlaylistPlatform? value)
    {
        _ = LoadPlaylistsAsync();
    }
}
