using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MusicEntertainmentApp.Models;
using MusicEntertainmentApp.Services;
using System.Collections.ObjectModel;

namespace MusicEntertainmentApp.ViewModels;

public partial class ArtistsViewModel : BaseViewModel
{
    [ObservableProperty]
    private ObservableCollection<Artist> _artists = new();

    [ObservableProperty]
    private string _searchText = string.Empty;

    [ObservableProperty]
    private Artist? _selectedArtist;

    [ObservableProperty]
    private bool _isAddingNew;

    [ObservableProperty]
    private string _newArtistName = string.Empty;

    [ObservableProperty]
    private string _newArtistBio = string.Empty;

    public ArtistsViewModel(ISupabaseService supabaseService, ILogger<ArtistsViewModel> logger)
        : base(supabaseService, logger)
    {
        LoadArtists();
    }

    [RelayCommand]
    private async Task LoadArtistsAsync()
    {
        await ExecuteAsync(async () =>
        {
            var artists = await _supabaseService.GetArtistsAsync(search: SearchText);
            
            Artists.Clear();
            foreach (var artist in artists)
            {
                Artists.Add(artist);
            }
            
            _logger.LogInformation("Loaded {Count} artists", Artists.Count);
        });
    }

    [RelayCommand]
    private void StartAddNew()
    {
        IsAddingNew = true;
        NewArtistName = string.Empty;
        NewArtistBio = string.Empty;
    }

    [RelayCommand]
    private void CancelAdd()
    {
        IsAddingNew = false;
        NewArtistName = string.Empty;
        NewArtistBio = string.Empty;
    }

    [RelayCommand]
    private async Task SaveNewArtistAsync()
    {
        if (string.IsNullOrWhiteSpace(NewArtistName))
        {
            ShowError("Artist name is required.");
            return;
        }

        await ExecuteAsync(async () =>
        {
            var newArtist = new Artist
            {
                Name = NewArtistName.Trim(),
                Bio = string.IsNullOrWhiteSpace(NewArtistBio) ? null : NewArtistBio.Trim()
            };

            var createdArtist = await _supabaseService.CreateArtistAsync(newArtist);
            Artists.Add(createdArtist);
            
            ShowSuccess($"Artist '{createdArtist.Name}' created successfully.");
            CancelAdd();
        });
    }

    [RelayCommand]
    private async Task DeleteArtistAsync(Artist? artist)
    {
        if (artist == null) return;

        var result = System.Windows.MessageBox.Show(
            $"Are you sure you want to delete the artist '{artist.Name}'?",
            "Confirm Delete",
            System.Windows.MessageBoxButton.YesNo,
            System.Windows.MessageBoxImage.Warning);

        if (result == System.Windows.MessageBoxResult.Yes)
        {
            await ExecuteAsync(async () =>
            {
                await _supabaseService.DeleteArtistAsync(artist.Id);
                Artists.Remove(artist);
                ShowSuccess($"Artist '{artist.Name}' deleted successfully.");
            });
        }
    }

    private void LoadArtists()
    {
        _ = LoadArtistsAsync();
    }

    partial void OnSearchTextChanged(string value)
    {
        _ = LoadArtistsAsync();
    }
}
