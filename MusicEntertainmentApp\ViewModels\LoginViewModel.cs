using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MusicEntertainmentApp.Services;
using System.Windows;

namespace MusicEntertainmentApp.ViewModels;

public partial class LoginViewModel : BaseViewModel
{
    [ObservableProperty]
    private string _email = string.Empty;

    [ObservableProperty]
    private string _password = string.Empty;

    [ObservableProperty]
    private bool _rememberMe;

    [ObservableProperty]
    private bool _isSignUpMode;

    [ObservableProperty]
    private string _fullName = string.Empty;

    public LoginViewModel(ISupabaseService supabaseService, ILogger<LoginViewModel> logger)
        : base(supabaseService, logger)
    {
    }

    [RelayCommand]
    private async Task SignInAsync()
    {
        if (string.IsNullOrWhiteSpace(Email) || string.IsNullOrWhiteSpace(Password))
        {
            ShowError("Please enter both email and password.");
            return;
        }

        await ExecuteAsync(async () =>
        {
            var success = await _supabaseService.SignInAsync(Email, Password);
            if (success)
            {
                ShowSuccess("Signed in successfully!");
                
                // Close login window and show main window
                Application.Current.Windows.OfType<Window>()
                    .FirstOrDefault(w => w.GetType().Name == "LoginWindow")?.Close();
            }
            else
            {
                ShowError("Invalid email or password.");
            }
        });
    }

    [RelayCommand]
    private async Task SignUpAsync()
    {
        if (string.IsNullOrWhiteSpace(Email) || string.IsNullOrWhiteSpace(Password) || string.IsNullOrWhiteSpace(FullName))
        {
            ShowError("Please fill in all required fields.");
            return;
        }

        if (Password.Length < 6)
        {
            ShowError("Password must be at least 6 characters long.");
            return;
        }

        await ExecuteAsync(async () =>
        {
            var success = await _supabaseService.SignUpAsync(Email, Password, FullName);
            if (success)
            {
                ShowSuccess("Account created successfully! Please check your email for verification.");
                IsSignUpMode = false;
                ClearForm();
            }
            else
            {
                ShowError("Failed to create account. Please try again.");
            }
        });
    }

    [RelayCommand]
    private void ToggleMode()
    {
        IsSignUpMode = !IsSignUpMode;
        ClearMessages();
        ClearForm();
    }

    [RelayCommand]
    private void ForgotPassword()
    {
        // TODO: Implement forgot password functionality
        ShowError("Forgot password functionality will be implemented soon.");
    }

    private void ClearForm()
    {
        Email = string.Empty;
        Password = string.Empty;
        FullName = string.Empty;
        RememberMe = false;
    }

    partial void OnIsSignUpModeChanged(bool value)
    {
        ClearMessages();
    }
}
