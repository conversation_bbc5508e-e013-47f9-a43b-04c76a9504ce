using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace MusicEntertainmentApp.Models;

public class AppSetting : BaseEntity
{
    public Guid? UserId { get; set; }
    
    [Required]
    [StringLength(100)]
    public string SettingKey { get; set; } = string.Empty;
    
    public string SettingValueJson { get; set; } = "{}";
    
    [JsonIgnore]
    public object? SettingValue
    {
        get => JsonConvert.DeserializeObject(SettingValueJson);
        set => SettingValueJson = JsonConvert.SerializeObject(value);
    }
    
    // Navigation properties
    public virtual UserProfile? User { get; set; }
}

// Strongly typed settings classes
public class UserPreferences
{
    public string Theme { get; set; } = "Dark";
    public int PageSize { get; set; } = 50;
    public string DefaultExportPath { get; set; } = string.Empty;
    public bool ShowTooltips { get; set; } = true;
    public bool AutoSave { get; set; } = true;
    public Dictionary<string, bool> ColumnVisibility { get; set; } = new();
    public Dictionary<string, int> ColumnWidths { get; set; } = new();
}

public class ExportSettings
{
    public string DefaultFormat { get; set; } = "xlsx";
    public bool IncludeHeaders { get; set; } = true;
    public bool IncludeMetadata { get; set; } = false;
    public string DateFormat { get; set; } = "yyyy-MM-dd";
    public Dictionary<string, object> CustomFields { get; set; } = new();
}

public class PlaylistGeneratorSettings
{
    public double MinDurationHours { get; set; } = 2.5;
    public double MaxDurationHours { get; set; } = 3.0;
    public int MaxTracksPerAlbum { get; set; } = 2;
    public Dictionary<string, double> GenreBalance { get; set; } = new();
    public Dictionary<string, double> ArtistBalance { get; set; } = new();
    public bool ShuffleBeforeAssign { get; set; } = true;
}
