using MusicEntertainmentApp.Models;
using Supabase.Gotrue;

namespace MusicEntertainmentApp.Services;

public interface ISupabaseService
{
    // Authentication
    Task<bool> SignInAsync(string email, string password);
    Task<bool> SignUpAsync(string email, string password, string fullName);
    Task SignOutAsync();
    Task<UserProfile?> GetCurrentUserProfileAsync();
    bool IsAuthenticated { get; }
    
    // User Management
    Task<IEnumerable<UserProfile>> GetUsersAsync();
    Task<UserProfile?> GetUserByIdAsync(Guid id);
    Task<UserProfile> CreateUserAsync(UserProfile user);
    Task<UserProfile> UpdateUserAsync(UserProfile user);
    Task DeleteUserAsync(Guid id);
    
    // Artists
    Task<IEnumerable<Artist>> GetArtistsAsync(int page = 1, int pageSize = 50, string? search = null);
    Task<Artist?> GetArtistByIdAsync(Guid id);
    Task<Artist> CreateArtistAsync(Artist artist);
    Task<Artist> UpdateArtistAsync(Artist artist);
    Task DeleteArtistAsync(Guid id);
    
    // Labels
    Task<IEnumerable<Label>> GetLabelsAsync();
    Task<Label?> GetLabelByIdAsync(Guid id);
    Task<Label> CreateLabelAsync(Label label);
    Task<Label> UpdateLabelAsync(Label label);
    Task DeleteLabelAsync(Guid id);
    
    // Releases
    Task<IEnumerable<Release>> GetReleasesAsync(int page = 1, int pageSize = 50, string? search = null, ReleaseStatus? status = null);
    Task<Release?> GetReleaseByIdAsync(Guid id);
    Task<Release> CreateReleaseAsync(Release release);
    Task<Release> UpdateReleaseAsync(Release release);
    Task DeleteReleaseAsync(Guid id);
    
    // Tracks
    Task<IEnumerable<Track>> GetTracksAsync(int page = 1, int pageSize = 50, string? search = null, TrackStatus? status = null);
    Task<Track?> GetTrackByIdAsync(Guid id);
    Task<Track> CreateTrackAsync(Track track);
    Task<Track> UpdateTrackAsync(Track track);
    Task DeleteTrackAsync(Guid id);
    Task<IEnumerable<Track>> GetTracksByReleaseAsync(Guid releaseId);
    Task<IEnumerable<Track>> GetTracksByArtistAsync(Guid artistId);
    
    // Playlists
    Task<IEnumerable<Playlist>> GetPlaylistsAsync(int page = 1, int pageSize = 50, string? search = null, PlaylistPlatform? platform = null);
    Task<Playlist?> GetPlaylistByIdAsync(Guid id);
    Task<Playlist> CreatePlaylistAsync(Playlist playlist);
    Task<Playlist> UpdatePlaylistAsync(Playlist playlist);
    Task DeletePlaylistAsync(Guid id);
    Task<IEnumerable<PlaylistTrack>> GetPlaylistTracksAsync(Guid playlistId);
    Task<PlaylistTrack> AddTrackToPlaylistAsync(Guid playlistId, Guid trackId, int? position = null);
    Task RemoveTrackFromPlaylistAsync(Guid playlistId, Guid trackId);
    
    // Bundles
    Task<IEnumerable<Bundle>> GetBundlesAsync();
    Task<Bundle?> GetBundleByIdAsync(Guid id);
    Task<Bundle> CreateBundleAsync(Bundle bundle);
    Task<Bundle> UpdateBundleAsync(Bundle bundle);
    Task DeleteBundleAsync(Guid id);
    Task<IEnumerable<BundleTrack>> GetBundleTracksAsync(Guid bundleId);
    Task<BundleTrack> AddTrackToBundleAsync(Guid bundleId, Guid trackId, bool isAutoAssigned = false);
    Task RemoveTrackFromBundleAsync(Guid bundleId, Guid trackId);
    Task<IEnumerable<Track>> GetUnassignedTracksAsync();
    
    // Files
    Task<string> UploadFileAsync(Stream fileStream, string fileName, string? entityType = null, Guid? entityId = null);
    Task<FileEntity> CreateFileRecordAsync(FileEntity file);
    Task<IEnumerable<FileEntity>> GetFilesAsync(string? entityType = null, Guid? entityId = null);
    Task DeleteFileAsync(Guid fileId);
    Task<Stream> DownloadFileAsync(string filePath);
    
    // Settings
    Task<AppSetting?> GetSettingAsync(string key, Guid? userId = null);
    Task<AppSetting> SaveSettingAsync(string key, object value, Guid? userId = null);
    Task<IEnumerable<AppSetting>> GetUserSettingsAsync(Guid userId);
    
    // Statistics and Analytics
    Task<Dictionary<string, int>> GetDashboardStatsAsync();
    Task<IEnumerable<object>> GetReleasesByMonthAsync(int year);
    Task<IEnumerable<object>> GetTopArtistsAsync(int limit = 10);
    Task<IEnumerable<object>> GetTopPlaylistsAsync(int limit = 10);
}
