using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace MusicEntertainmentApp.Models;

public class Track : BaseEntity
{
    [Required]
    [StringLength(300)]
    public string Title { get; set; } = string.Empty;
    
    public Guid? ArtistId { get; set; }
    
    public Guid? ReleaseId { get; set; }
    
    public int? TrackNumber { get; set; }
    
    public int? DurationSeconds { get; set; }
    
    public string? IsrcCode { get; set; }
    
    public string? AudioFileUrl { get; set; }
    
    public TrackStatus Status { get; set; } = TrackStatus.Active;
    
    // JSON field for tags
    public string TagsJson { get; set; } = "[]";
    
    [JsonIgnore]
    public List<string> Tags
    {
        get => JsonConvert.DeserializeObject<List<string>>(TagsJson) ?? new List<string>();
        set => TagsJson = JsonConvert.SerializeObject(value);
    }
    
    // JSON field for additional metadata
    public string MetadataJson { get; set; } = "{}";
    
    [JsonIgnore]
    public Dictionary<string, object> Metadata
    {
        get => JsonConvert.DeserializeObject<Dictionary<string, object>>(MetadataJson) ?? new Dictionary<string, object>();
        set => MetadataJson = JsonConvert.SerializeObject(value);
    }
    
    // Navigation properties
    public virtual Artist? Artist { get; set; }
    public virtual Release? Release { get; set; }
    public virtual UserProfile? Creator { get; set; }
    public virtual ICollection<PlaylistTrack> PlaylistTracks { get; set; } = new List<PlaylistTrack>();
    public virtual ICollection<BundleTrack> BundleTracks { get; set; } = new List<BundleTrack>();
    public virtual ICollection<FileEntity> Files { get; set; } = new List<FileEntity>();
    
    // Computed properties
    public string ArtistName => Artist?.Name ?? "Unknown Artist";
    public string ReleaseName => Release?.Title ?? "Unknown Release";
    public string DurationFormatted => DurationSeconds.HasValue ? TimeSpan.FromSeconds(DurationSeconds.Value).ToString(@"mm\:ss") : "00:00";
    public string TagsDisplay => string.Join(", ", Tags);
}
