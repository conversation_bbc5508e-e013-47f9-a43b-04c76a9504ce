<Window x:Class="MusicEntertainmentApp.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Music Entertainment App - Login" 
        Height="600" 
        Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Margin="0,0,0,20">
            <TextBlock Text="🎵" FontSize="48" HorizontalAlignment="Center" Margin="0,20,0,10" Foreground="White"/>
            <TextBlock Text="Music Entertainment App" 
                       FontSize="20" 
                       FontWeight="Bold" 
                       HorizontalAlignment="Center" 
                       Margin="0,0,0,20"
                       Foreground="White"/>
        </StackPanel>

        <!-- Login Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="40,20">
                
                <!-- Mode Toggle -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
                    <Button Content="Sign In" 
                            Style="{StaticResource MaterialDesignFlatButton}"
                            Command="{Binding ToggleModeCommand}"
                            Visibility="{Binding IsSignUpMode, Converter={StaticResource BoolToVisConverter}}"/>
                    <Button Content="Sign Up" 
                            Style="{StaticResource MaterialDesignFlatButton}"
                            Command="{Binding ToggleModeCommand}"
                            Visibility="{Binding IsSignUpMode, Converter={StaticResource BoolToVisConverter}, ConverterParameter=Inverse}"/>
                </StackPanel>

                <!-- Sign Up Fields -->
                <TextBox materialDesign:HintAssist.Hint="Full Name"
                         Text="{Binding FullName, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,15"
                         Visibility="{Binding IsSignUpMode, Converter={StaticResource BoolToVisConverter}}"/>

                <!-- Email -->
                <TextBox materialDesign:HintAssist.Hint="Email"
                         Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,15"/>

                <!-- Password -->
                <PasswordBox materialDesign:HintAssist.Hint="Password"
                             materialDesign:PasswordBoxAssist.Password="{Binding Password, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,0,15"/>

                <!-- Remember Me (Sign In only) -->
                <CheckBox Content="Remember me"
                          IsChecked="{Binding RememberMe}"
                          Margin="0,0,0,20"
                          Visibility="{Binding IsSignUpMode, Converter={StaticResource BoolToVisConverter}, ConverterParameter=Inverse}"/>

                <!-- Action Buttons -->
                <Button Content="{Binding IsSignUpMode, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Create Account|Sign In'}"
                        Command="{Binding IsSignUpMode, Converter={StaticResource BoolToCommandConverter}, ConverterParameter='SignUpCommand|SignInCommand'}"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        IsDefault="True"
                        Margin="0,0,0,10"/>

                <!-- Forgot Password (Sign In only) -->
                <Button Content="Forgot Password?"
                        Command="{Binding ForgotPasswordCommand}"
                        Style="{StaticResource MaterialDesignFlatButton}"
                        HorizontalAlignment="Center"
                        Visibility="{Binding IsSignUpMode, Converter={StaticResource BoolToVisConverter}, ConverterParameter=Inverse}"/>

                <!-- Loading Indicator -->
                <ProgressBar IsIndeterminate="True"
                             Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}}"
                             Margin="0,20,0,0"/>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}"
                           Foreground="Red"
                           TextWrapping="Wrap"
                           Margin="0,10,0,0"
                           Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

                <!-- Success Message -->
                <TextBlock Text="{Binding SuccessMessage}"
                           Foreground="Green"
                           TextWrapping="Wrap"
                           Margin="0,10,0,0"
                           Visibility="{Binding SuccessMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20">
            <TextBlock Text="© 2024 Music Entertainment App" 
                       FontSize="12" 
                       Foreground="{DynamicResource MaterialDesignBodyLight}"/>
        </StackPanel>

    </Grid>
</Window>
