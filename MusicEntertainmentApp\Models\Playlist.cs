using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace MusicEntertainmentApp.Models;

public class Playlist : BaseEntity
{
    [Required]
    [StringLength(300)]
    public string Name { get; set; } = string.Empty;
    
    public PlaylistPlatform Platform { get; set; }
    
    public string? PlatformId { get; set; }
    
    public string? PlatformUrl { get; set; }
    
    public string? CuratorName { get; set; }
    
    [EmailAddress]
    public string? CuratorEmail { get; set; }
    
    public int FollowerCount { get; set; } = 0;
    
    public string? Description { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    // JSON field for additional metadata
    public string MetadataJson { get; set; } = "{}";
    
    [JsonIgnore]
    public Dictionary<string, object> Metadata
    {
        get => JsonConvert.DeserializeObject<Dictionary<string, object>>(MetadataJson) ?? new Dictionary<string, object>();
        set => MetadataJson = JsonConvert.SerializeObject(value);
    }
    
    // Navigation properties
    public virtual UserProfile? Creator { get; set; }
    public virtual ICollection<PlaylistTrack> PlaylistTracks { get; set; } = new List<PlaylistTrack>();
    
    // Computed properties
    public int TrackCount => PlaylistTracks?.Count ?? 0;
    public string PlatformDisplay => Platform.ToString();
    public string StatusDisplay => IsActive ? "Online" : "Offline";
}

public class PlaylistTrack : BaseEntity
{
    public Guid PlaylistId { get; set; }
    
    public Guid TrackId { get; set; }
    
    public int? Position { get; set; }
    
    public DateTime AddedDate { get; set; } = DateTime.UtcNow;
    
    public bool IsFeatured { get; set; } = false;
    
    // JSON field for additional metadata
    public string MetadataJson { get; set; } = "{}";
    
    [JsonIgnore]
    public Dictionary<string, object> Metadata
    {
        get => JsonConvert.DeserializeObject<Dictionary<string, object>>(MetadataJson) ?? new Dictionary<string, object>();
        set => MetadataJson = JsonConvert.SerializeObject(value);
    }
    
    // Navigation properties
    public virtual Playlist? Playlist { get; set; }
    public virtual Track? Track { get; set; }
}
