using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace MusicEntertainmentApp.Models;

public class Artist : BaseEntity
{
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;
    
    public string? Bio { get; set; }
    
    public string? ImageUrl { get; set; }
    
    public string? SpotifyId { get; set; }
    
    public string? AppleMusicId { get; set; }
    
    public string? YouTubeChannelId { get; set; }
    
    // JSON field for social media links
    public string SocialLinksJson { get; set; } = "{}";
    
    [JsonIgnore]
    public Dictionary<string, string> SocialLinks
    {
        get => JsonConvert.DeserializeObject<Dictionary<string, string>>(SocialLinksJson) ?? new Dictionary<string, string>();
        set => SocialLinksJson = JsonConvert.SerializeObject(value);
    }
    
    // JSON field for additional metadata
    public string MetadataJson { get; set; } = "{}";
    
    [JsonIgnore]
    public Dictionary<string, object> Metadata
    {
        get => JsonConvert.DeserializeObject<Dictionary<string, object>>(MetadataJson) ?? new Dictionary<string, object>();
        set => MetadataJson = JsonConvert.SerializeObject(value);
    }
    
    // Navigation properties
    public virtual ICollection<Release> Releases { get; set; } = new List<Release>();
    public virtual ICollection<Track> Tracks { get; set; } = new List<Track>();
    public virtual UserProfile? Creator { get; set; }
}
